package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 📏 平行线测量助手 - 核心管理类
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010!\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0014\u0018\u0000 :2\u00020\u0001:\u0001:B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0019\u001a\u00020\u001aJ\u0006\u0010\u001b\u001a\u00020\u0013J\u0006\u0010\u001c\u001a\u00020\u0013J\u0006\u0010\u001d\u001a\u00020\nJ\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020 0\u001fJ\u0006\u0010!\u001a\u00020\u0006J\u0010\u0010\"\u001a\u00020\n2\u0006\u0010#\u001a\u00020$H\u0002J\u001e\u0010%\u001a\u00020\n2\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\u00062\u0006\u0010)\u001a\u00020\u0006J\u0010\u0010*\u001a\u00020\n2\u0006\u0010#\u001a\u00020$H\u0002J\u0010\u0010+\u001a\u00020\n2\u0006\u0010#\u001a\u00020$H\u0002J\u0006\u0010,\u001a\u00020\nJ\u0016\u0010-\u001a\u00020\u00132\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010.\u001a\u00020\u0017J\u0010\u0010/\u001a\u00020\n2\u0006\u0010#\u001a\u00020$H\u0002J\u000e\u00100\u001a\u00020\n2\u0006\u0010#\u001a\u00020$J\u000e\u00101\u001a\u00020\n2\u0006\u0010#\u001a\u00020$J\u0006\u00102\u001a\u00020\u0013J\u0006\u00103\u001a\u00020\u0013J\u0006\u00104\u001a\u00020\u0013J\b\u00105\u001a\u00020\u0013H\u0002J\u0006\u00106\u001a\u00020\u0013J\u0014\u00107\u001a\u00020\u00132\f\u00108\u001a\b\u0012\u0004\u0012\u00020\u00130\u0012J\u0006\u00109\u001a\u00020\u001aR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00040\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006;"}, d2 = {"Lcom/touptek/measurerealize/utils/ParallelLinesMeasureHelper;", "", "()V", "activeMeasurement", "Lcom/touptek/measurerealize/utils/ParallelLinesMeasurement;", "draggedPointIndex", "", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "isDraggingPoint", "", "isInitialized", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "", "originalBitmap", "Landroid/graphics/Bitmap;", "selectedMeasurement", "addNewMeasurement", "", "clearAllMeasurements", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurementData", "", "Lcom/touptek/measurerealize/utils/ParallelLinesMeasurementData;", "getMeasurementCount", "handleTouchDown", "touchPoint", "Landroid/graphics/PointF;", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "handleTouchMove", "handleTouchUp", "hasSelectedMeasurement", "init", "bitmap", "isInImageContentArea", "isNearAnyMeasurement", "isPointOnMeasurement", "onScaleChanged", "pauseMeasurement", "reset", "resetInteractionState", "resumeMeasurement", "setMeasurementUpdateCallback", "callback", "startNewMeasurement", "Companion", "app_debug"})
public final class ParallelLinesMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.ParallelLinesMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "ParallelLinesMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final float CLICK_TOLERANCE = 20.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private static final float DEFAULT_LINE_LENGTH = 200.0F;
    private static final float DEFAULT_LINE_SPACING = 100.0F;
    private com.touptek.measurerealize.TpImageView imageView;
    private android.graphics.Bitmap originalBitmap;
    private boolean isInitialized = false;
    private final java.util.List<com.touptek.measurerealize.utils.ParallelLinesMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.ParallelLinesMeasurement selectedMeasurement;
    private com.touptek.measurerealize.utils.ParallelLinesMeasurement activeMeasurement;
    private boolean isDraggingPoint = false;
    private int draggedPointIndex = -1;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public ParallelLinesMeasureHelper() {
        super();
    }
    
    /**
     * 🚀 初始化助手 - 与其他Helper保持一致的初始化模式
     */
    public final void init(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🔄 重置交互状态
     */
    private final void resetInteractionState() {
    }
    
    /**
     * 📏 开始新的平行线测量 - 在屏幕中心创建默认平行线
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 📏 添加新的平行线测量（在测量模式下）
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String addNewMeasurement() {
        return null;
    }
    
    /**
     * 🎯 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 处理触摸事件 - 与LineMeasureHelper保持一致的交互逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    private final boolean handleTouchDown(android.graphics.PointF touchPoint) {
        return false;
    }
    
    private final boolean handleTouchMove(android.graphics.PointF touchPoint) {
        return false;
    }
    
    private final boolean handleTouchUp(android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 检查触摸点是否在图像内容区域
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 📊 获取所有测量数据（用于渲染）
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.ParallelLinesMeasurementData> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🔄 缩放变化时同步坐标 - 与其他Helper保持一致
     */
    public final void onScaleChanged() {
    }
    
    /**
     * 🎯 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🎯 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🎯 检查触摸点是否靠近任何测量 - 与其他Helper保持一致
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 检查触摸点是否在任何测量上（用于智能模式激活）
     */
    public final boolean isPointOnMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔄 清除选中状态 - 与其他Helper保持一致
     */
    public final void clearSelection() {
    }
    
    /**
     * 🗑️ 删除选中的测量
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🧹 清理所有测量数据
     */
    public final void clearAllMeasurements() {
    }
    
    /**
     * ⏸️ 暂停测量 - 与其他Helper保持一致
     */
    public final void pauseMeasurement() {
    }
    
    /**
     * ▶️ 恢复测量 - 与其他Helper保持一致
     */
    public final void resumeMeasurement() {
    }
    
    /**
     * 🧹 重置助手 - 与其他Helper保持一致
     */
    public final void reset() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/touptek/measurerealize/utils/ParallelLinesMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "DEFAULT_LINE_LENGTH", "DEFAULT_LINE_SPACING", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}