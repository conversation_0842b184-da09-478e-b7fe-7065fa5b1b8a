package com.touptek.measurerealize.utils

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import android.view.MotionEvent
import com.touptek.measurerealize.TpImageView
import kotlin.math.*

/**
 * 🎨 专业级测量覆盖层 - 超越iPad体验的可视化
 * 
 * 核心特性：
 * - 多层测量渲染：支持同时显示多种测量类型
 * - 专业视觉效果：动态高亮、流畅动画、精美渲染
 * - 智能坐标转换：与TpImageView完美协作
 * - 高性能绘制：优化的Canvas绘制算法
 */
class MeasurementOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 🎨 专业级绘制工具
    private val linePaint = Paint().apply {
        color = Color.parseColor("#2196F3")
        strokeWidth = 6f
        style = Paint.Style.STROKE
        isAntiAlias = true
        strokeCap = Paint.Cap.ROUND
    }

    private val pointPaint = Paint().apply {
        color = Color.parseColor("#4CAF50")
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val highlightPointPaint = Paint().apply {
        color = Color.parseColor("#FF5722")
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val textPaint = Paint().apply {
        color = Color.WHITE
        textSize = 48f
        isAntiAlias = true
        typeface = Typeface.DEFAULT_BOLD
    }

    private val arcPaint = Paint().apply {
        color = Color.parseColor("#FF9800")
        strokeWidth = 4f
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    // 测量数据和图像视图引用
    private var measurementData: MeasurementData? = null
    private var allMeasurementData: List<AngleMeasurementData> = emptyList() // 支持多个角度测量
    private var allFourPointAngleMeasurementData: List<FourPointAngleMeasurementData> = emptyList() // 支持多个四点角度测量
    private var allPointMeasurementData: List<PointMeasurementData> = emptyList() // 支持多个点测量
    private var allLineMeasurementData: List<LineMeasurementData> = emptyList() // 支持多个线段测量
    private var allHorizonLineMeasurementData: List<HorizonLineMeasurementData> = emptyList() // 支持多个水平线测量
    private var allVerticalLineMeasurementData: List<VerticalLineMeasurementData> = emptyList() // 支持多个垂直线测量
    private var allParallelLinesMeasurementData: List<ParallelLinesMeasurementData> = emptyList() // 支持多个平行线测量
    private var imageView: TpImageView? = null

    // 测量助手引用
    private var angleMeasureHelper: AngleMeasureHelper? = null
    private var fourPointAngleHelper: FourPointAngleHelper? = null
    private var pointMeasureHelper: PointMeasureHelper? = null
    private var lineMeasureHelper: LineMeasureHelper? = null
    private var horizonLineMeasureHelper: HorizonLineMeasureHelper? = null
    private var verticalLineMeasureHelper: VerticalLineMeasureHelper? = null
    private var parallelLinesMeasureHelper: ParallelLinesMeasureHelper? = null

    fun setImageView(imageView: TpImageView) {
        this.imageView = imageView
    }

    fun setAngleMeasureHelper(helper: AngleMeasureHelper) {
        this.angleMeasureHelper = helper
    }

    fun setFourPointAngleHelper(helper: FourPointAngleHelper) {
        this.fourPointAngleHelper = helper
    }

    fun setPointMeasureHelper(helper: PointMeasureHelper) {
        this.pointMeasureHelper = helper
    }

    fun setLineMeasureHelper(helper: LineMeasureHelper) {
        this.lineMeasureHelper = helper
    }

    fun setHorizonLineMeasureHelper(helper: HorizonLineMeasureHelper) {
        this.horizonLineMeasureHelper = helper
    }

    fun setVerticalLineMeasureHelper(helper: VerticalLineMeasureHelper) {
        this.verticalLineMeasureHelper = helper
    }

    fun setParallelLinesMeasureHelper(helper: ParallelLinesMeasureHelper) {
        this.parallelLinesMeasureHelper = helper
    }

    fun setMeasurementData(data: MeasurementData?) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] setMeasurementData called with: $data")
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] Current overlay size: ${width}x${height}")
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] Current visibility: $visibility")

        measurementData = data
        android.util.Log.d("MeasurementOverlay", "📊 Setting measurement data: $data")

        if (data != null) {
            when (data) {
                is AngleMeasurementData -> {
                    android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] AngleMeasurementData - points.size: ${data.points.size}, angle: ${data.angle}°")
                    data.points.forEachIndexed { index, point ->
                        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] point[$index]: $point")
                    }
                }
                else -> {
                    android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] Other measurement data type: ${data::class.simpleName}")
                }
            }

            // 🎯 确保覆盖层可见并强制重绘
            visibility = View.VISIBLE
            bringToFront() // 确保覆盖层在最前面

            // 强制重绘
            invalidate()
            requestLayout()

            android.util.Log.d("MeasurementOverlay", "✅ Overlay updated and invalidated")
        } else {
            android.util.Log.d("MeasurementOverlay", "🧹 Clearing measurement data")
        }
    }

    fun clearMeasurement() {
        measurementData = null
        allMeasurementData = emptyList()
        allFourPointAngleMeasurementData = emptyList()
        allPointMeasurementData = emptyList()
        allLineMeasurementData = emptyList()
        android.util.Log.d("MeasurementOverlay", "🧹 Cleared all measurement data to prevent overlay artifacts")
        invalidate()
    }

    /**
     * 🎯 设置多个角度测量数据 - 支持同时显示多个角度
     */
    fun setAllAngleMeasurementData(dataList: List<AngleMeasurementData>) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] setAllAngleMeasurementData called with ${dataList.size} measurements")

        allMeasurementData = dataList
        measurementData = null // 清除单个测量数据，使用多个测量数据

        dataList.forEachIndexed { index, data ->
            android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] measurement[$index]: points.size=${data.points.size}, angle=${data.angle}°, isDragging=${data.isDragging}")
        }

        if (dataList.isNotEmpty()) {
            // 🎯 确保覆盖层可见并强制重绘
            visibility = View.VISIBLE
            bringToFront()
            invalidate()
            requestLayout()
            android.util.Log.d("MeasurementOverlay", "✅ Multi-angle overlay updated and invalidated")
        } else {
            android.util.Log.d("MeasurementOverlay", "🧹 No angle measurements to display")
        }
    }

    /**
     * 🎯 设置多个四点角度测量数据 - 支持同时显示多个四点角度
     */
    fun setAllFourPointAngleMeasurementData(dataList: List<FourPointAngleMeasurementData>) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] setAllFourPointAngleMeasurementData called with ${dataList.size} measurements")

        allFourPointAngleMeasurementData = dataList
        measurementData = null // 清除单个测量数据，使用多个测量数据
        allMeasurementData = emptyList() // 清除三点角度数据

        dataList.forEachIndexed { index, data ->
            android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] four-point measurement[$index]: points.size=${data.points.size}, angle=${data.angle}°, isDragging=${data.isDragging}")
        }

        if (dataList.isNotEmpty()) {
            // 🎯 确保覆盖层可见并强制重绘
            visibility = View.VISIBLE
            bringToFront()
            invalidate()
            requestLayout()
            android.util.Log.d("MeasurementOverlay", "✅ Multi four-point angle overlay updated and invalidated")
        } else {
            android.util.Log.d("MeasurementOverlay", "🧹 No four-point angle measurements to display")
        }
    }

    /**
     * 🎯 更新三点角度测量数据 - 混合模式支持
     */
    fun updateAngleMeasurements(dataList: List<AngleMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateAngleMeasurements called with ${dataList.size} measurements")
        allMeasurementData = dataList
        invalidate()
    }

    /**
     * 🎯 更新四点角度测量数据 - 混合模式支持
     */
    fun updateFourPointAngleMeasurements(dataList: List<FourPointAngleMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateFourPointAngleMeasurements called with ${dataList.size} measurements")
        allFourPointAngleMeasurementData = dataList
        invalidate()
    }

    /**
     * 🎯 更新点测量数据 - 混合模式支持
     */
    fun updatePointMeasurements(dataList: List<PointMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updatePointMeasurements called with ${dataList.size} measurements")
        allPointMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新线段测量数据 - 混合模式支持
     */
    fun updateLineMeasurements(dataList: List<LineMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateLineMeasurements called with ${dataList.size} measurements")
        allLineMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新水平线测量数据 - 混合模式支持
     */
    fun updateHorizonLineMeasurements(dataList: List<HorizonLineMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateHorizonLineMeasurements called with ${dataList.size} measurements")
        allHorizonLineMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新垂直线测量数据 - 混合模式支持
     */
    fun updateVerticalLineMeasurements(dataList: List<VerticalLineMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateVerticalLineMeasurements called with ${dataList.size} measurements")
        allVerticalLineMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新平行线测量数据 - 混合模式支持
     */
    fun updateParallelLinesMeasurements(dataList: List<ParallelLinesMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateParallelLinesMeasurements called with ${dataList.size} measurements")
        allParallelLinesMeasurementData = dataList
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val imgView = imageView
        if (imgView == null) {
            return
        }

        // 🎯 绘制多个四点角度测量
        if (allFourPointAngleMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allFourPointAngleMeasurementData.size} four-point angle measurements")
            allFourPointAngleMeasurementData.forEach { fourPointAngleData ->
                drawFourPointAngleMeasurement(canvas, fourPointAngleData, imgView)
            }
        }

        // 🎯 绘制多个三点角度测量（与四点角度测量共存）
        if (allMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allMeasurementData.size} angle measurements")
            allMeasurementData.forEach { angleData ->
                drawAngleMeasurement(canvas, angleData, imgView)
            }
        }

        // 🎯 绘制多个点测量（与角度测量共存）
        if (allPointMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allPointMeasurementData.size} point measurements")
            allPointMeasurementData.forEach { pointData ->
                drawPointMeasurement(canvas, pointData, imgView)
            }
        }

        // 📏 绘制多个线段测量（与其他测量共存）
        if (allLineMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allLineMeasurementData.size} line measurements")
            allLineMeasurementData.forEach { lineData ->
                drawLineMeasurement(canvas, lineData, imgView)
            }
        }

        // 📏 绘制多个水平线测量（与其他测量共存）
        if (allHorizonLineMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allHorizonLineMeasurementData.size} horizon line measurements")
            allHorizonLineMeasurementData.forEach { horizonLineData ->
                drawHorizonLineMeasurement(canvas, horizonLineData, imgView)
            }
        }

        // 📏 绘制多个垂直线测量（与其他测量共存）
        if (allVerticalLineMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allVerticalLineMeasurementData.size} vertical line measurements")
            allVerticalLineMeasurementData.forEach { verticalLineData ->
                drawVerticalLineMeasurement(canvas, verticalLineData, imgView)
            }
        }

        // 📏 绘制多个平行线测量（与其他测量共存）
        if (allParallelLinesMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allParallelLinesMeasurementData.size} parallel lines measurements")
            allParallelLinesMeasurementData.forEach { parallelLinesData ->
                drawParallelLinesMeasurement(canvas, parallelLinesData, imgView)
            }
        }

        // 如果有任何测量数据，则不需要回退到单个测量数据绘制
        if (allFourPointAngleMeasurementData.isNotEmpty() || allMeasurementData.isNotEmpty() || allPointMeasurementData.isNotEmpty() || allLineMeasurementData.isNotEmpty() || allHorizonLineMeasurementData.isNotEmpty() || allVerticalLineMeasurementData.isNotEmpty() || allParallelLinesMeasurementData.isNotEmpty()) {
            return
        }

        // 🔄 回退到单个测量数据绘制
        val data = measurementData
        if (data == null) {
            return
        }

        when (data) {
            is AngleMeasurementData -> {
                drawAngleMeasurement(canvas, data, imgView)
            }
            is DistanceMeasurementData -> drawDistanceMeasurement(canvas, data, imgView)
            is RectangleMeasurementData -> drawRectangleMeasurement(canvas, data, imgView)
            is CircleMeasurementData -> drawCircleMeasurement(canvas, data, imgView)
            is ThreePointCircleMeasurementData -> drawThreePointCircleMeasurement(canvas, data, imgView)
            is FourPointAngleMeasurementData -> drawFourPointAngleMeasurement(canvas, data, imgView)
            is ParallelLinesMeasurementData -> drawParallelLinesMeasurement(canvas, data, imgView)
            is EllipseMeasurementData -> drawEllipseMeasurement(canvas, data, imgView)
            is MultiPointPathMeasurementData -> drawMultiPointPathMeasurement(canvas, data, imgView)
            is PointMeasurementData -> drawPointMeasurement(canvas, data, imgView)
            is LineMeasurementData -> drawLineMeasurement(canvas, data, imgView)
            is HorizonLineMeasurementData -> drawHorizonLineMeasurement(canvas, data, imgView)
        }
    }

    /**
     * 🎯 绘制角度测量 - 专业级三点角度可视化
     */
    private fun drawAngleMeasurement(canvas: Canvas, data: AngleMeasurementData, imageView: TpImageView) {
        if (data.points.size < 3) {
            return
        }

        val vertex = data.points[0]    // 顶点
        val point1 = data.points[1]    // 第一个点
        val point2 = data.points[2]    // 第二个点

        // 🎨 根据状态选择绘制样式
        val lineColor = when {
            data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        val currentLinePaint = Paint(linePaint).apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isDragging) 8f else 6f
        }

        // 🎨 绘制角度线条
        canvas.drawLine(vertex.x, vertex.y, point1.x, point1.y, currentLinePaint)
        canvas.drawLine(vertex.x, vertex.y, point2.x, point2.y, currentLinePaint)

        // 🎯 绘制角度弧线
        drawAngleArc(canvas, vertex, point1, point2, data.angle, data.isSelected, data.isDragging)

        // 🎨 绘制控制点
        val pointRadius = when {
            data.isDragging -> 24f
            data.isSelected -> 18f
            else -> 14f
        }
        val currentPointPaint = when {
            data.isDragging -> highlightPointPaint
            data.isSelected -> Paint(pointPaint).apply { color = Color.parseColor("#4CAF50") }
            else -> pointPaint
        }

        canvas.drawCircle(vertex.x, vertex.y, pointRadius, currentPointPaint)
        canvas.drawCircle(point1.x, point1.y, pointRadius, currentPointPaint)
        canvas.drawCircle(point2.x, point2.y, pointRadius, currentPointPaint)

        // 🎨 绘制角度文本
        drawAngleText(canvas, vertex, data.angle, data.isDragging, data.isSelected)
    }

    /**
     * 🎨 绘制角度弧线 - 专业级弧线渲染
     */
    private fun drawAngleArc(canvas: Canvas, vertex: PointF, point1: PointF, point2: PointF, angle: Double, isSelected: Boolean = false, isDragging: Boolean = false) {
        val arcRadius = 60f
        
        // 计算两条线的角度
        val angle1 = atan2((point1.y - vertex.y).toDouble(), (point1.x - vertex.x).toDouble())
        val angle2 = atan2((point2.y - vertex.y).toDouble(), (point2.x - vertex.x).toDouble())
        
        var startAngle = Math.toDegrees(angle1).toFloat()
        var sweepAngle = Math.toDegrees(angle2 - angle1).toFloat()
        
        // 确保弧线绘制方向正确
        if (sweepAngle > 180) sweepAngle -= 360
        if (sweepAngle < -180) sweepAngle += 360
        
        val rect = RectF(
            vertex.x - arcRadius,
            vertex.y - arcRadius,
            vertex.x + arcRadius,
            vertex.y + arcRadius
        )
        
        // 🎨 根据状态调整弧线样式
        val currentArcPaint = Paint(arcPaint).apply {
            color = when {
                isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
                else -> Color.parseColor("#2196F3") // 默认蓝色
            }
            strokeWidth = if (isSelected || isDragging) 6f else 4f
        }

        canvas.drawArc(rect, startAngle, sweepAngle, false, currentArcPaint)
    }

    /**
     * 🎨 绘制角度文本 - 智能位置和专业样式
     */
    private fun drawAngleText(canvas: Canvas, vertex: PointF, angle: Double, isDragging: Boolean, isSelected: Boolean = false) {
        val angleText = String.format("%.1f°", angle)
        val textBounds = Rect()
        textPaint.getTextBounds(angleText, 0, angleText.length, textBounds)

        // 🎯 智能文本位置（在顶点附近）
        val textX = vertex.x + 80f
        val textY = vertex.y - 40f

        // 🎨 绘制文本背景（专业级设计）
        val backgroundPaint = Paint().apply {
            color = when {
                isDragging -> Color.argb(200, 233, 30, 99) // 拖拽时粉色背景
                isSelected -> Color.argb(200, 76, 175, 80) // 选中时绿色背景
                else -> Color.argb(180, 0, 0, 0) // 正常时黑色背景
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val padding = 12f
        canvas.drawRoundRect(
            textX - padding,
            textY - textBounds.height() - padding,
            textX + textBounds.width() + padding,
            textY + padding,
            12f, 12f,
            backgroundPaint
        )

        // 绘制文本
        canvas.drawText(angleText, textX, textY, textPaint)
    }

    // 其他测量类型的绘制方法将在后续添加
    private fun drawDistanceMeasurement(canvas: Canvas, data: DistanceMeasurementData, imageView: TpImageView) {
        // TODO: 实现距离测量绘制
    }

    private fun drawRectangleMeasurement(canvas: Canvas, data: RectangleMeasurementData, imageView: TpImageView) {
        // TODO: 实现矩形测量绘制
    }

    private fun drawCircleMeasurement(canvas: Canvas, data: CircleMeasurementData, imageView: TpImageView) {
        // TODO: 实现圆形测量绘制
    }

    private fun drawThreePointCircleMeasurement(canvas: Canvas, data: ThreePointCircleMeasurementData, imageView: TpImageView) {
        // TODO: 实现三点圆测量绘制
    }

    private fun drawFourPointAngleMeasurement(canvas: Canvas, data: FourPointAngleMeasurementData, imageView: TpImageView) {
        if (data.points.size < 4) return

        val p0 = data.points[0] // 第一条线起点
        val p1 = data.points[1] // 第一条线终点
        val p2 = data.points[2] // 第二条线起点
        val p3 = data.points[3] // 第二条线终点

        // 根据状态选择绘制样式
        val lineColor = when {
            data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        val currentLinePaint = Paint(linePaint).apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isDragging) 8f else 6f
        }

        // 注意：原始线段将在延长线绘制方法中重新绘制，这里先绘制作为底层
        canvas.drawLine(p0.x, p0.y, p1.x, p1.y, currentLinePaint)
        canvas.drawLine(p2.x, p2.y, p3.x, p3.y, currentLinePaint)

        // 绘制四个控制点
        val pointRadius = when {
            data.isDragging -> 24f
            data.isSelected -> 18f
            else -> 14f
        }
        val currentPointPaint = when {
            data.isDragging -> highlightPointPaint
            data.isSelected -> Paint(pointPaint).apply { color = Color.parseColor("#4CAF50") }
            else -> pointPaint
        }

        // 绘制四个点，使用不同颜色区分
        val pointColors = listOf(
            Color.parseColor("#F44336"),  // 红色点1
            Color.parseColor("#FF9800"),  // 橙色点2
            Color.parseColor("#9C27B0"),  // 紫色点3
            Color.parseColor("#607D8B")   // 蓝灰色点4
        )

        data.points.forEachIndexed { index, point ->
            val pointPaint = Paint(currentPointPaint).apply {
                color = if (data.isSelected || data.isDragging) pointColors[index] else this.color
            }
            canvas.drawCircle(point.x, point.y, pointRadius, pointPaint)
        }

        // 如果有交点，绘制交点和角度弧线
        if (data.isValid && data.intersection != null) {
            // 绘制智能延长线
            drawFourPointExtensionLines(canvas, data.points, data.intersection, data.isSelected, data.isDragging)

            // 绘制交点
            val intersectionPaint = Paint().apply {
                color = Color.parseColor("#E91E63") // 粉色交点
                style = Paint.Style.FILL
                isAntiAlias = true
            }
            canvas.drawCircle(data.intersection.x, data.intersection.y, 8f, intersectionPaint)

            // 绘制角度弧线
            drawFourPointAngleArc(canvas, data.points, data.intersection, data.angle, data.isSelected, data.isDragging)

            // 绘制角度文本
            drawFourPointAngleText(canvas, data.intersection, data.angle, data.isDragging, data.isSelected)
        }
    }

    /**
     * 🎨 绘制四点角度弧线
     */
    private fun drawFourPointAngleArc(canvas: Canvas, points: List<PointF>, intersection: PointF, angle: Double, isSelected: Boolean = false, isDragging: Boolean = false) {
        val arcRadius = 60f

        // 计算两条线的方向向量
        val vec1 = PointF(
            (points[0].x + points[1].x) / 2f - intersection.x,
            (points[0].y + points[1].y) / 2f - intersection.y
        )
        val vec2 = PointF(
            (points[2].x + points[3].x) / 2f - intersection.x,
            (points[2].y + points[3].y) / 2f - intersection.y
        )

        // 计算角度
        val angle1 = atan2(vec1.y.toDouble(), vec1.x.toDouble())
        val angle2 = atan2(vec2.y.toDouble(), vec2.x.toDouble())

        var startAngle = Math.toDegrees(angle1).toFloat()
        var sweepAngle = Math.toDegrees(angle2 - angle1).toFloat()

        // 确保绘制较小的角度
        if (sweepAngle > 180f) {
            sweepAngle -= 360f
        } else if (sweepAngle < -180f) {
            sweepAngle += 360f
        }

        if (sweepAngle < 0) {
            startAngle += sweepAngle
            sweepAngle = -sweepAngle
        }

        val rect = RectF(
            intersection.x - arcRadius,
            intersection.y - arcRadius,
            intersection.x + arcRadius,
            intersection.y + arcRadius
        )

        // 根据状态调整弧线样式
        val currentArcPaint = Paint(arcPaint).apply {
            color = when {
                isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
                else -> Color.parseColor("#FFEB3B") // 默认黄色
            }
            strokeWidth = if (isSelected || isDragging) 6f else 4f
        }

        canvas.drawArc(rect, startAngle, sweepAngle, false, currentArcPaint)
    }

    /**
     * 🎨 绘制四点角度文本
     */
    private fun drawFourPointAngleText(canvas: Canvas, intersection: PointF, angle: Double, isDragging: Boolean, isSelected: Boolean = false) {
        val angleText = String.format("%.1f°", angle)
        val textBounds = Rect()
        textPaint.getTextBounds(angleText, 0, angleText.length, textBounds)

        // 智能文本位置（在交点附近）
        val textX = intersection.x + 80f
        val textY = intersection.y - 40f

        // 绘制文本背景
        val backgroundPaint = Paint().apply {
            color = when {
                isDragging -> Color.argb(200, 233, 30, 99) // 拖拽时粉色背景
                isSelected -> Color.argb(200, 76, 175, 80) // 选中时绿色背景
                else -> Color.argb(180, 0, 0, 0) // 正常时黑色背景
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val padding = 12f
        canvas.drawRoundRect(
            textX - padding,
            textY - textBounds.height() - padding,
            textX + textBounds.width() + padding,
            textY + padding,
            12f, 12f,
            backgroundPaint
        )

        // 绘制文本
        canvas.drawText(angleText, textX, textY, textPaint)
    }

    private fun drawParallelLinesMeasurement(canvas: Canvas, data: ParallelLinesMeasurementData, imageView: TpImageView) {
        // TODO: 实现平行线测量绘制
    }

    private fun drawEllipseMeasurement(canvas: Canvas, data: EllipseMeasurementData, imageView: TpImageView) {
        // TODO: 实现椭圆测量绘制
    }

    private fun drawMultiPointPathMeasurement(canvas: Canvas, data: MultiPointPathMeasurementData, imageView: TpImageView) {
        // TODO: 实现多点路径测量绘制
    }

    /**
     * 🎯 处理触摸事件 - 委托给测量助手
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        android.util.Log.d("MeasurementOverlay", "🎯 Touch event: ${event.action}, forwarding to MeasurementManager")

        // 在混合共存模式下，将所有触摸事件转发给 TpImageView 的 MeasurementManager 处理
        // MeasurementManager 会智能判断是测量操作还是图像拖拽，包括空白区域点击
        forwardEventToImageView(event)
        return false
    }

    /**
     * 🔄 将事件转发给TpImageView
     */
    private fun forwardEventToImageView(event: MotionEvent) {
        imageView?.let { imgView ->
            val adjustedEvent = MotionEvent.obtain(event)
            val handled = imgView.dispatchTouchEvent(adjustedEvent)
            adjustedEvent.recycle()
            android.util.Log.d("MeasurementOverlay", "📱 TpImageView handled event: $handled")
        }
    }

    /**
     * 🎨 绘制四点角度智能延长线
     */
    private fun drawFourPointExtensionLines(canvas: Canvas, points: List<PointF>, intersection: PointF?, isSelected: Boolean, isDragging: Boolean) {
        if (points.size < 4) return

        // 获取智能延长线数据
        val extensions = fourPointAngleHelper?.calculateExtensionLines(points, intersection) ?: return

        // 延长线绘制样式
        val extensionPaint = Paint().apply {
            color = when {
                isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                isSelected -> Color.parseColor("#757575") // 选中时灰色
                else -> Color.parseColor("#BDBDBD") // 默认浅灰色
            }
            strokeWidth = 2f
            style = Paint.Style.STROKE
            isAntiAlias = true
            pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
        }

        // 原始线段绘制样式（用于区分延长部分）
        val originalLinePaint = Paint().apply {
            color = when {
                isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
                else -> Color.parseColor("#2196F3") // 默认蓝色
            }
            strokeWidth = if (isSelected || isDragging) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
        }

        // 绘制智能延长线（只到交点）
        extensions.forEach { (start, end) ->
            canvas.drawLine(start.x, start.y, end.x, end.y, extensionPaint)
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing extension line: ($start) -> ($end)")
        }

        // 重新绘制原始线段，确保它们在延长线之上
        canvas.drawLine(points[0].x, points[0].y, points[1].x, points[1].y, originalLinePaint)
        canvas.drawLine(points[2].x, points[2].y, points[3].x, points[3].y, originalLinePaint)

        // android.util.Log.d("MeasurementOverlay", "🎨 Drew ${extensions.size} extension lines to intersection")
    }

    /**
     * 🎯 绘制点测量 - 红色圆点+白色十字
     */
    private fun drawPointMeasurement(canvas: Canvas, data: PointMeasurementData, imageView: TpImageView) {
        if (data.points.isEmpty()) {
            return
        }

        val point = data.points[0] // 单个点

        // 🎨 根据状态选择绘制样式
        val pointRadius = when {
            data.isDragging -> 24f
            data.isSelected -> 20f
            else -> 16f
        }

        // 🎨 绘制红色圆点
        val circlePaint = Paint().apply {
            color = Color.parseColor("#FF5722") // Material Design Red 600
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 如果选中，添加绿色边框
        if (data.isSelected) {
            val borderPaint = Paint().apply {
                color = Color.parseColor("#4CAF50") // Material Design Green 500
                style = Paint.Style.STROKE
                strokeWidth = 4f
                isAntiAlias = true
            }
            canvas.drawCircle(point.x, point.y, pointRadius + 2f, borderPaint)
        }

        canvas.drawCircle(point.x, point.y, pointRadius, circlePaint)

        // 🎨 绘制白色十字
        val crossPaint = Paint().apply {
            color = Color.WHITE
            strokeWidth = when {
                data.isDragging -> 4f
                data.isSelected -> 4f
                else -> 3f
            }
            style = Paint.Style.STROKE
            strokeCap = Paint.Cap.ROUND
            isAntiAlias = true
        }

        val crossLength = pointRadius * 0.6f

        // 绘制十字的水平线
        canvas.drawLine(
            point.x - crossLength, point.y,
            point.x + crossLength, point.y,
            crossPaint
        )

        // 绘制十字的垂直线
        canvas.drawLine(
            point.x, point.y - crossLength,
            point.x, point.y + crossLength,
            crossPaint
        )

        // 🎨 如果选中，绘制坐标文本
        if (data.isSelected) {
            val textPaint = Paint().apply {
                color = Color.WHITE
                textSize = 36f
                isAntiAlias = true
                typeface = Typeface.DEFAULT_BOLD
                setShadowLayer(2f, 1f, 1f, Color.BLACK)
            }

            val text = String.format("(%.0f, %.0f)", point.x, point.y)
            val textX = point.x - textPaint.measureText(text) / 2f
            val textY = point.y - pointRadius - 20f

            canvas.drawText(text, textX, textY, textPaint)
        }
    }

    /**
     * 📏 绘制线段测量 - 与AngleMeasureHelper和PointMeasureHelper保持一致的状态视觉反馈
     */
    private fun drawLineMeasurement(canvas: Canvas, data: LineMeasurementData, imageView: TpImageView) {
        if (data.points.size < 2) {
            return
        }

        val startPoint = data.points[0]
        val endPoint = data.points[1]

        // 🎨 根据状态选择绘制样式 - 与AngleMeasureHelper保持一致
        val lineColor = when {
            data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        // 🎨 线段绘制画笔
        val linePaint = Paint().apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isDragging) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 端点绘制画笔 - 根据状态变化
        val pointRadius = when {
            data.isDragging -> 24f
            data.isSelected -> 20f
            else -> 16f
        }

        val endPointPaint = Paint().apply {
            color = when {
                data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
                else -> Color.parseColor("#2196F3") // 默认蓝色
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 🎨 长度文本画笔
        val lengthTextPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isDragging -> 40f
                data.isSelected -> 38f
                else -> 36f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
        }

        // 🎨 文本背景画笔 - 根据状态变化颜色
        val textBackgroundPaint = Paint().apply {
            color = lineColor
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 📏 绘制线段
        canvas.drawLine(startPoint.x, startPoint.y, endPoint.x, endPoint.y, linePaint)

        // 🎯 绘制端点 - 与AngleMeasureHelper保持一致的样式
        canvas.drawCircle(startPoint.x, startPoint.y, pointRadius, endPointPaint)
        canvas.drawCircle(endPoint.x, endPoint.y, pointRadius, endPointPaint)

        // 📏 绘制长度文本
        val midX = (startPoint.x + endPoint.x) / 2
        val midY = (startPoint.y + endPoint.y) / 2 - 30f // 线段中点上方30像素

        val lengthText = String.format("%.1f px", data.length)
        val textWidth = lengthTextPaint.measureText(lengthText)
        val textHeight = lengthTextPaint.textSize

        // 绘制文本背景（圆角矩形）
        val padding = when {
            data.isDragging -> 12f
            data.isSelected -> 10f
            else -> 8f
        }
        val backgroundRect = RectF(
            midX - textWidth / 2 - padding,
            midY - textHeight / 2 - padding,
            midX + textWidth / 2 + padding,
            midY + textHeight / 2 + padding
        )
        canvas.drawRoundRect(backgroundRect, 8f, 8f, textBackgroundPaint)

        // 绘制长度文本
        canvas.drawText(lengthText, midX, midY + textHeight / 4, lengthTextPaint)
    }

    /**
     * 📏 绘制水平线测量 - 专门的水平约束线段绘制
     */
    private fun drawHorizonLineMeasurement(canvas: Canvas, data: HorizonLineMeasurementData, imageView: TpImageView) {
        if (data.viewPoints.size < 2) {
            return
        }

        val leftPoint = data.viewPoints[0]
        val rightPoint = data.viewPoints[1]

        // 🎨 根据状态选择绘制样式
        val lineColor = when {
            data.isEditing -> Color.parseColor("#FF5722") // 编辑时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        // 🎨 水平线绘制画笔 - 比普通线段稍粗以突出水平特性
        val linePaint = Paint().apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isEditing) 10f else 8f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 端点绘制画笔 - 左右端点不同样式以区分功能
        val pointRadius = when {
            data.isEditing -> 26f
            data.isSelected -> 22f
            else -> 18f
        }

        // 左端点 - 可自由移动（圆形）
        val leftPointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 右端点 - 只能水平移动（方形）
        val rightPointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 🎨 水平参考线画笔（虚线）
        val referencePaint = Paint().apply {
            color = Color.parseColor("#80FF5722") // 半透明橙色
            strokeWidth = 2f
            style = Paint.Style.STROKE
            isAntiAlias = true
            pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
        }

        // 📏 绘制主水平线
        canvas.drawLine(leftPoint.x, leftPoint.y, rightPoint.x, rightPoint.y, linePaint)

        // 🎯 绘制水平参考线（选中或编辑时显示）
        if (data.isSelected || data.isEditing) {
            val extendLength = 100f // 延长线长度
            canvas.drawLine(
                leftPoint.x - extendLength, data.baselineY,
                rightPoint.x + extendLength, data.baselineY,
                referencePaint
            )
        }

        // 🎯 绘制端点 - 左端点圆形，右端点方形
        canvas.drawCircle(leftPoint.x, leftPoint.y, pointRadius, leftPointPaint)

        // 右端点绘制为方形以区分功能
        val halfSize = pointRadius * 0.8f
        canvas.drawRect(
            rightPoint.x - halfSize, rightPoint.y - halfSize,
            rightPoint.x + halfSize, rightPoint.y + halfSize,
            rightPointPaint
        )

        // 📏 绘制长度文本
        data.textPosition?.let { textPos ->
            val lengthTextPaint = Paint().apply {
                color = Color.WHITE
                textSize = when {
                    data.isEditing -> 42f
                    data.isSelected -> 40f
                    else -> 38f
                }
                isAntiAlias = true
                textAlign = Paint.Align.CENTER
            }

            val textBackgroundPaint = Paint().apply {
                color = lineColor
                style = Paint.Style.FILL
                isAntiAlias = true
            }

            val lengthText = String.format("%.1f px", data.length)
            val textWidth = lengthTextPaint.measureText(lengthText)
            val textHeight = lengthTextPaint.textSize

            // 绘制文本背景
            val padding = when {
                data.isEditing -> 14f
                data.isSelected -> 12f
                else -> 10f
            }
            val backgroundRect = RectF(
                textPos.x - textWidth / 2 - padding,
                textPos.y - textHeight / 2 - padding,
                textPos.x + textWidth / 2 + padding,
                textPos.y + textHeight / 2 + padding
            )
            canvas.drawRoundRect(backgroundRect, 10f, 10f, textBackgroundPaint)

            // 绘制长度文本
            canvas.drawText(lengthText, textPos.x, textPos.y + textHeight / 4, lengthTextPaint)
        }
    }

    /**
     * 📏 绘制垂直线测量 - 专门的垂直约束线段绘制
     */
    private fun drawVerticalLineMeasurement(canvas: Canvas, data: VerticalLineMeasurementData, imageView: TpImageView) {
        if (data.viewPoints.size < 2) {
            return
        }

        val topPoint = data.viewPoints[0]
        val bottomPoint = data.viewPoints[1]

        // 🎨 根据状态选择绘制样式
        val lineColor = when {
            data.isEditing -> Color.parseColor("#FF5722") // 编辑时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        // 🎨 垂直线绘制画笔 - 比普通线段稍粗以突出垂直特性
        val linePaint = Paint().apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isEditing) 10f else 8f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 端点绘制画笔 - 上下端点不同样式以区分功能
        val pointRadius = when {
            data.isEditing -> 26f
            data.isSelected -> 22f
            else -> 18f
        }

        // 上端点 - 可自由移动（圆形）
        val topPointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 下端点 - 只能垂直移动（方形）
        val bottomPointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 🎨 垂直参考线画笔（虚线）
        val referencePaint = Paint().apply {
            color = Color.parseColor("#80FF5722") // 半透明橙色
            strokeWidth = 2f
            style = Paint.Style.STROKE
            isAntiAlias = true
            pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
        }

        // 📏 绘制主垂直线
        canvas.drawLine(topPoint.x, topPoint.y, bottomPoint.x, bottomPoint.y, linePaint)

        // 🎯 绘制垂直参考线（选中或编辑时显示）
        if (data.isSelected || data.isEditing) {
            val extendLength = 100f // 延长线长度
            canvas.drawLine(
                data.baselineX, topPoint.y - extendLength,
                data.baselineX, bottomPoint.y + extendLength,
                referencePaint
            )
        }

        // 🎯 绘制端点 - 上端点圆形，下端点方形
        canvas.drawCircle(topPoint.x, topPoint.y, pointRadius, topPointPaint)

        // 下端点绘制为方形以区分功能
        val halfSize = pointRadius * 0.8f
        canvas.drawRect(
            bottomPoint.x - halfSize, bottomPoint.y - halfSize,
            bottomPoint.x + halfSize, bottomPoint.y + halfSize,
            bottomPointPaint
        )

        // 📏 绘制长度文本
        data.textPosition?.let { textPos ->
            val lengthTextPaint = Paint().apply {
                color = Color.WHITE
                textSize = when {
                    data.isEditing -> 42f
                    data.isSelected -> 40f
                    else -> 38f
                }
                isAntiAlias = true
                textAlign = Paint.Align.CENTER
            }

            val textBackgroundPaint = Paint().apply {
                color = lineColor
                style = Paint.Style.FILL
                isAntiAlias = true
            }

            val lengthText = String.format("%.1f px", data.length)
            val textWidth = lengthTextPaint.measureText(lengthText)
            val textHeight = lengthTextPaint.textSize

            // 绘制文本背景
            val padding = when {
                data.isEditing -> 14f
                data.isSelected -> 12f
                else -> 10f
            }
            val backgroundRect = RectF(
                textPos.x - textWidth / 2 - padding,
                textPos.y - textHeight / 2 - padding,
                textPos.x + textWidth / 2 + padding,
                textPos.y + textHeight / 2 + padding
            )
            canvas.drawRoundRect(backgroundRect, 10f, 10f, textBackgroundPaint)

            // 绘制长度文本
            canvas.drawText(lengthText, textPos.x, textPos.y + textHeight / 4, lengthTextPaint)
        }
    }

    /**
     * 📏 绘制平行线测量 - 专业级平行线可视化
     */
    private fun drawParallelLinesMeasurement(canvas: Canvas, data: ParallelLinesMeasurementData, imageView: TpImageView) {
        if (data.viewPoints.size < 4) return

        val line1Start = data.viewPoints[0]
        val line1End = data.viewPoints[1]
        val line2Start = data.viewPoints[2]
        val line2End = data.viewPoints[3]

        // 🎨 线段画笔
        val linePaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722") // 编辑状态：橙色
                data.isSelected -> Color.parseColor("#4CAF50") // 选中状态：绿色
                else -> Color.parseColor("#2196F3") // 默认状态：蓝色
            }
            strokeWidth = when {
                data.isSelected -> 4f
                else -> 3f
            }
            style = Paint.Style.STROKE
            isAntiAlias = true
        }

        // 🎨 端点画笔
        val pointRadius = when {
            data.isSelected -> 12f
            else -> 10f
        }
        val pointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 🎨 垂直连接线画笔（虚线）
        val perpendicularPaint = Paint().apply {
            color = Color.parseColor("#80FF5722") // 半透明橙色
            strokeWidth = 2f
            style = Paint.Style.STROKE
            isAntiAlias = true
            pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
        }

        // 📏 绘制两条平行线
        canvas.drawLine(line1Start.x, line1Start.y, line1End.x, line1End.y, linePaint)
        canvas.drawLine(line2Start.x, line2Start.y, line2End.x, line2End.y, linePaint)

        // 🎯 绘制垂直连接线（从Line2起点到Line1的垂足）
        val perpFoot = calculatePerpendicularFoot(line2Start, line1Start, line1End)
        canvas.drawLine(line2Start.x, line2Start.y, perpFoot.x, perpFoot.y, perpendicularPaint)

        // 🎯 绘制端点
        canvas.drawCircle(line1Start.x, line1Start.y, pointRadius, pointPaint)
        canvas.drawCircle(line1End.x, line1End.y, pointRadius, pointPaint)
        canvas.drawCircle(line2Start.x, line2Start.y, pointRadius, pointPaint)

        // Line2的右端点用方形表示（自动计算的点）
        val halfSize = pointRadius * 0.8f
        canvas.drawRect(
            line2End.x - halfSize, line2End.y - halfSize,
            line2End.x + halfSize, line2End.y + halfSize,
            pointPaint
        )

        // 📊 绘制距离文本
        val textPosition = data.textPosition ?: run {
            // 如果没有指定文本位置，计算默认位置（垂直连接线中点）
            val midX = (line2Start.x + perpFoot.x) / 2f
            val midY = (line2Start.y + perpFoot.y) / 2f
            PointF(midX, midY)
        }

        val distanceText = String.format("%.1f px", data.distance)

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isSelected -> 16f
                else -> 14f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
        }

        val textBackgroundPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#CC000000") // 半透明黑色
                data.isSelected -> Color.parseColor("#CC4CAF50") // 半透明绿色
                else -> Color.parseColor("#CC2196F3") // 半透明蓝色
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 计算文本尺寸
        val textBounds = android.graphics.Rect()
        textPaint.getTextBounds(distanceText, 0, distanceText.length, textBounds)
        val textWidth = textBounds.width().toFloat()
        val textHeight = textBounds.height().toFloat()

        // 绘制文本背景
        val padding = when {
            data.isSelected -> 12f
            else -> 10f
        }
        val backgroundRect = RectF(
            textPosition.x - textWidth / 2 - padding,
            textPosition.y - textHeight / 2 - padding,
            textPosition.x + textWidth / 2 + padding,
            textPosition.y + textHeight / 2 + padding
        )
        canvas.drawRoundRect(backgroundRect, 10f, 10f, textBackgroundPaint)

        // 绘制距离文本
        canvas.drawText(distanceText, textPosition.x, textPosition.y + textHeight / 4, textPaint)
    }

    /**
     * 🔧 计算垂足位置
     */
    private fun calculatePerpendicularFoot(point: PointF, lineStart: PointF, lineEnd: PointF): PointF {
        val A = lineEnd.y - lineStart.y
        val B = lineStart.x - lineEnd.x
        val C = lineEnd.x * lineStart.y - lineStart.x * lineEnd.y

        val denominator = A * A + B * B
        if (denominator == 0f) return point // 线段长度为0

        val footX = (B * (B * point.x - A * point.y) - A * C) / denominator
        val footY = (A * (-B * point.x + A * point.y) - B * C) / denominator

        return PointF(footX, footY)
    }

    // onInterceptTouchEvent方法已移除，因为View类不支持此方法（仅ViewGroup支持）
}
