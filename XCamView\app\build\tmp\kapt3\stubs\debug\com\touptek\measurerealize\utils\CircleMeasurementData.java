package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * ⭕ 圆形测量数据 - 圆心+半径点测量
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0010\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0006\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\u000f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\nH\u00c6\u0003JA\u0010\u0018\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\n2\b\u0010\u001a\u001a\u0004\u0018\u00010\u001bH\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u000eR\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\r\u00a8\u0006 "}, d2 = {"Lcom/touptek/measurerealize/utils/CircleMeasurementData;", "Lcom/touptek/measurerealize/utils/MeasurementData;", "points", "", "Landroid/graphics/PointF;", "radius", "", "area", "perimeter", "isDragging", "", "(Ljava/util/List;DDDZ)V", "getArea", "()D", "()Z", "getPerimeter", "getPoints", "()Ljava/util/List;", "getRadius", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_debug"})
public final class CircleMeasurementData extends com.touptek.measurerealize.utils.MeasurementData {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<android.graphics.PointF> points = null;
    private final double radius = 0.0;
    private final double area = 0.0;
    private final double perimeter = 0.0;
    private final boolean isDragging = false;
    
    /**
     * ⭕ 圆形测量数据 - 圆心+半径点测量
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.CircleMeasurementData copy(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, double radius, double area, double perimeter, boolean isDragging) {
        return null;
    }
    
    /**
     * ⭕ 圆形测量数据 - 圆心+半径点测量
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * ⭕ 圆形测量数据 - 圆心+半径点测量
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * ⭕ 圆形测量数据 - 圆心+半径点测量
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public CircleMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, double radius, double area, double perimeter, boolean isDragging) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getPoints() {
        return null;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    public final double getRadius() {
        return 0.0;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double getArea() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double getPerimeter() {
        return 0.0;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean isDragging() {
        return false;
    }
}