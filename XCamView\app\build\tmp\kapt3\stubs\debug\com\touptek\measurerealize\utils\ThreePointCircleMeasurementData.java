package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🔺 三点圆测量数据
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0013\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u000f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u000bH\u00c6\u0003JK\u0010\u001c\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u000b2\b\u0010\u001e\u001a\u0004\u0018\u00010\u001fH\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020#H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0011R\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000eR\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000e\u00a8\u0006$"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreePointCircleMeasurementData;", "Lcom/touptek/measurerealize/utils/MeasurementData;", "points", "", "Landroid/graphics/PointF;", "center", "radius", "", "area", "perimeter", "isDragging", "", "(Ljava/util/List;Landroid/graphics/PointF;DDDZ)V", "getArea", "()D", "getCenter", "()Landroid/graphics/PointF;", "()Z", "getPerimeter", "getPoints", "()Ljava/util/List;", "getRadius", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_debug"})
public final class ThreePointCircleMeasurementData extends com.touptek.measurerealize.utils.MeasurementData {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<android.graphics.PointF> points = null;
    @org.jetbrains.annotations.NotNull
    private final android.graphics.PointF center = null;
    private final double radius = 0.0;
    private final double area = 0.0;
    private final double perimeter = 0.0;
    private final boolean isDragging = false;
    
    /**
     * 🔺 三点圆测量数据
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.ThreePointCircleMeasurementData copy(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, @org.jetbrains.annotations.NotNull
    android.graphics.PointF center, double radius, double area, double perimeter, boolean isDragging) {
        return null;
    }
    
    /**
     * 🔺 三点圆测量数据
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 🔺 三点圆测量数据
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 🔺 三点圆测量数据
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public ThreePointCircleMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, @org.jetbrains.annotations.NotNull
    android.graphics.PointF center, double radius, double area, double perimeter, boolean isDragging) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getPoints() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF getCenter() {
        return null;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double getRadius() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double getArea() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    public final double getPerimeter() {
        return 0.0;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean isDragging() {
        return false;
    }
}